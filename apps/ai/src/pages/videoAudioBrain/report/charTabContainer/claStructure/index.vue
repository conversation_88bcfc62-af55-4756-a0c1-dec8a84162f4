<script lang="ts" setup>
import { MergedSegmentProps } from "@/pages/videoAudioBrain/entity";
import { ysEmpt, ysIcon } from "@ys/ui";
import { inject, Ref, ref, watch, computed } from "vue";

// const props = defineProps<{
//   data: any;
// }>();
const mergedSegments = inject<Ref<MergedSegmentProps[]>>("mergedSegments");
const roleChats = inject<Ref<any>>("roleChats");

let list: any = ref([]);

const colors = [
  {
    text: "讲授",
    colos: "#14c9c9",
  },
  {
    text: "讨论",
    colos: "#722ed1",
  },
  {
    text: "直观演示",
    colos: "#eb2f96",
  },
  {
    text: "练习",
    colos: "#fa541c",
  },
  {
    text: "读书指导",
    colos: "#fadb14",
  },
  {
    text: "任务驱动",
    colos: "#a0d911",
  },
  {
    text: "参观教学",
    colos: "#27a3fa",
  },
  {
    text: "现场教学",
    colos: "#2f54eb",
  },
  {
    text: "自主学习",
    colos: "#65789b",
  },
];

const getColor = (name: string) => {
  return colors.find((item) => item.text == name)?.colos;
};

// function getRandIndex() {
//   return Math.floor(Math.random() * (4 - 0 + 1)) + 0;
// }

function getTypeList(val: string) {
  if (val) {
    if (val.indexOf("、") != -1) {
      return val.split("、");
    } else if (val.indexOf("/") != -1) {
      return val.split("/");
    }
    return [val];
  }
  return [];
}

function time2Sec(time: string) {
  time = time.trim();
  let s = 0;
  let hour = Number(time.split(":")[0]);
  let min = Number(time.split(":")[1]);
  let sec = time.split(":")[2] ? Number(time.split(":")[2]) : Number("00");
  s = hour * 3600 + min * 60 + sec;
  return s;
}

const percentageComputed = (endTime: any, startTime: any, maxTime: any) => {
  if (!endTime || !startTime || !maxTime) {
    return 0;
  }
  return ((endTime - startTime) / maxTime) * 100;
};

watch(
  () => mergedSegments?.value,
  (val) => {
    if (!val) return;
    if (val) {
      list.value = val.map((item: MergedSegmentProps) => {
        item.questions.forEach((q) => {
          q.s = time2Sec(q.startTime);
          q.e = time2Sec(q.endTime);
          q.bloom = q["布鲁姆"];
          q.blackboards.forEach((b: any) => {
            b.images = JSON.parse(b.result);
            return b;
          });
        });

        return {
          ...item,
          s: item["时间"].split("-")[0],
          e: item["时间"].split("-")[1],
          startTime: time2Sec(item["时间"].split("-")[0]),
          endTime: time2Sec(item["时间"].split("-")[1]),
        };
      });
      if (list.value.length > 0) {
        const maxTime = list.value[list.value.length - 1].endTime;
        list.value = list.value.map((item: any) => ({
          ...item,
          percentage: percentageComputed(item.endTime, item.startTime, maxTime),
          // percentage: ((item.endTime - item.startTime) / maxTime) * 100,
        }));
      }
    }
  },

  { immediate: true }
);

// watch(
//   () => roleChats?.value,
//   (val) => {
//     if (!val) return;
//     const res = val[3];
//     const data = res[1];
//     if (data) {
//       const answer = data.answer;
//       let answer_a = answer.replace(/\n/g, "");
//       try {
//         const _answer = JSON.parse(answer_a);
//         list.value = _answer.map((item: any) => {
//           return {
//             ...item,
//             s: item["时间"].split("-")[0],
//             e: item["时间"].split("-")[1],
//             startTime: time2Sec(item["时间"].split("-")[0]),
//             endTime: time2Sec(item["时间"].split("-")[1]),
//           };
//         });
//         if (list.value.length > 0) {
//           const maxTime = list.value[list.value.length - 1].endTime;
//           list.value = list.value.map((item: any) => ({
//             ...item,
//             percentage: ((item.endTime - item.startTime) / maxTime) * 100,
//           }));
//         }
//       } catch (error) {
//         console.log("JSON.parse", error);
//         list.value = [];
//       }
//     }
//   },

//   { immediate: true }
// );

function handle2Seek(val: number) {
  const aiVideo = document.getElementById("ai-video") as HTMLVideoElement;
  const aiVideo1 = document.getElementById("ai-video1") as HTMLVideoElement;
  aiVideo!.currentTime = val;
  aiVideo1!.currentTime = val;
}

const getBloomClass = (bloom: string) => {
  if (bloom === "创造") {
    return "tag1";
  }
  switch(bloom) {
    case "创造":
      return "tag1";
    case "评价":
      return "tag2";
    case "分析":
      return "tag3";
    case "应用":
      return "tag4";
    case "理解":
      return "tag5";
    case "记忆":
      return "tag6";
    default:
      return "hidden";
  }
}

const activeIndex = ref<number>(-1);

// 管理每个结构项的展开/收起状态
const expandedItems = ref<Set<number>>(new Set());

/**
 * 切换结构项的展开/收起状态
 * @param index 结构项索引
 */
const toggleExpanded = (index: number) => {
  if (expandedItems.value.has(index)) {
    expandedItems.value.delete(index);
  } else {
    expandedItems.value.add(index);
  }
};

/**
 * 检查结构项是否展开
 * @param index 结构项索引
 * @returns 是否展开
 */
const isExpanded = (index: number) => {
  return expandedItems.value.has(index);
};

const processNum = computed(() => {
  if (!list.value || list.value.length === 0) {
    return [];
  }
  const timeStr = list.value[list.value.length - 1].e;
  const minutes = Number(timeStr.split(':')[1]);
  const segmentSize = minutes / 7;
  const result = [];
  for (let i = 0; i < 7; i++) {
    result.push(Math.floor(segmentSize * (i + 1)));
  }
  return result;
});

const scrollToStructureItem = (index: number, time: number) => {
  activeIndex.value = index;
  const structureItems = document.querySelectorAll('.structure-item');
  if (structureItems && structureItems.length > index) {
    const targetElement = structureItems[index] as HTMLElement;
    if (targetElement) {
      targetElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  }
  toggleExpanded(index);
  handle2Seek(time);
}
</script>

<template>
  <div class="class-structure">
    <ysEmpt v-if="list.length === 0" />

    <div v-if="list.length > 0" class="process">
      <div class="progress-bar">
        <a-tooltip
          v-for="(item, index) in list"
          :key="index"
          color="white"
          placement="bottom"
        >
          <template #title>
            <h4>{{ item["教学环节"] }}</h4>
            <p style="color: #262626;">{{ item["时间"] }}</p>
          </template>
          <div class="progress-bar-item" :style="{ width: `${item.percentage}%`, background: activeIndex === index ? '#2997FF': '#7AC8FF' }" @click="scrollToStructureItem(index, item.startTime)"></div>
        </a-tooltip>
      </div>
      <p class="process-num">
        <span>0</span>
        <span v-for="(item, index) in processNum" :key="index">{{ item }}</span>
      </p>
    </div>

    <div v-for="(item, index) in list" :key="index" class="structure-item">
      <div class="structure-item-head">
        <div class="index">{{ index + 1 }}</div>
        <div class="title">{{ item["教学环节"] }}</div>
        <div class="percentage">
          ({{ item.percentage.toFixed(2) }}%)
        </div>
        <div class="time-line">
          <span @click="handle2Seek(item.startTime)" class="time">{{
            item.s
          }}</span>
          <span>-</span>
          <span @click="handle2Seek(item.endTime)" class="time">{{
            item.e
          }}</span>
        </div>
        <div class="tag">
          <a-tag
            v-for="(type, tagIndex) in getTypeList(
              item['教学方式']
            )"
            :key="tagIndex"
            :color="getColor(type)"
          >
            {{ type }}
          </a-tag>
        </div>
        <div class="expand-arrow" @click="toggleExpanded(index)">
          <!-- <span :class="['arrow-icon', { expanded: isExpanded(index) }]">⌵</span> -->
           <ys-icon :class="['arrow-icon', { expanded: isExpanded(index) }]" type="iconxiajiantou" />
        </div>
      </div>
      <div v-show="isExpanded(index)" class="structure-item-body">
        <!-- <div v-for="c in item['教学内容']">{{ c }}</div> -->
        <div class="structure-item-body-list">
          <div class="structure-item-body-listItem generalize">
            <div class="label2">内容概括</div>
            <div class="content2">
              <p v-for="c in item['教学内容']">
                {{ c }}
              </p>
            </div>
          </div>
          <div
            class="structure-item-body-listItem generalize"
            v-for="(q, qi) in item.questions"
          >
            <div class="label2" @click="handle2Seek(q.s)">
              问题{{ qi + 1 }}
            </div>
            <div class="content2">
              <p>
                  <span
                    :class="[
                      'label-tag',
                      getBloomClass(q.bloom),
                    ]"
                    >{{ q.bloom }}</span
                  >
                  {{ q["问题"] }}
                </p>
                <div class="images2">
                  <div
                    class="images2-item"
                    v-for="b in q.blackboards"
                  >
                    <div
                      class="images2-item-li"
                      v-for="imgUrl in b.images"
                    >
                      <a-image
                        :width="160"
                        :height="90"
                        :src="
                          'data:image/png;base64,' +
                          imgUrl
                        "
                      />
                    </div>
                  </div>
                </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.class-structure {
  position: relative;
  width: 100%;
  height: 100%;
  // overflow-y: auto;
  font-size: 16px;
}
.process {
  position: sticky;
  top: 0;
  left: 0;
  padding-top: 20px;
  background-color: #fff;
  .progress-bar {
    display: flex;
    column-gap: 3px;
    width: 100%;
    height: 16px;
    &-item {
      width: 10%;
      height: 100%;
      border-radius: 2px;
      cursor: pointer;
    }
  }
  .process-num {
    display: flex;
    justify-content: space-between;
    margin-top: 5px;
  }
}

.structure-item {
  margin-top: 20px;
  scroll-margin-top: 70px;
}
.structure-item-head {
  padding: 0 8px;
  display: flex;
  align-items: center;
  height: 40px;
  background: #ecf5ff;
  border-radius: 4px;
  .index {
    border-radius: 50%;
    width: 20px;
    height: 20px;
    background: #007aff;
    font-size: 12px;
    color: #fff;
    text-align: center;
    line-height: 20px;
  }
  .title {
    color: #262626;
    font-weight: 700;
    padding: 0 8px;
  }
  .percentage {
    color: #8c8c8c;
  }
  .time-line {
    color: #007aff;
    padding: 0 20px;
    .time {
      cursor: pointer;
    }
  }
  .expand-arrow {
    margin-left: auto;
    padding: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 2px;
    transition: background-color 0.2s ease;

    .arrow-icon {
      color: #8C8C8C;
      transition: transform 0.2s ease;

      &.expanded {
        transform: rotate(180deg);
      }
    }
  }
}
.structure-item-body {
  padding: 16px 0;
  .structure-item-body-list {
    width: 100%;
    .structure-item-body-listItem {
      margin-bottom: 20px;
      display: flex;
      width: 100%;
      &:last-child {
        margin-bottom: 0;
      }
      .label2 {
        flex: 0 0 68px;
        width: 68px;
        height: 24px;
        font-size: 13px;
        line-height: 22px;
        text-align: center;
        background: #e6f2ff;
        border-radius: 4px 4px 4px 4px;
        box-sizing: border-box;
        color: #007aff;
        border: 1px solid #7ac8ff;
        cursor: pointer;
      }
      .content2 {
        flex: 1;
        margin-left: 8px;
        .label-tag {
          display: inline-block;
          font-size: 13px;
          width: 46px;
          height: 22px;
          line-height: 22px;
          text-align: center;
          border-radius: 10px;
        }
        .tag1 {
          color: #F1584E;
          background-color: #F1584E26;
        }
        .tag2 {
          color: #F48A2E;
          background-color: #F48A2E26;
        }
        .tag3 {
          color: #FACD0B;
          background-color: #FACD0B26;
        }
        .tag4 {
          color: #59BE7F;
          background-color: #59BE7F26;
        }
        .tag5 {
          color: #4AB4D0;
          background-color: #4AB4D026;
        }
        .tag6 {
          color: #8F7BD3;
          background-color: #8F7BD326;
        }
        .hidden {
          display: none;
        }
        .label3-list {
          display: flex;
          align-items: center;
          list-style: none;
          margin: 0;
          padding: 0;
          li {
            margin-right: 8px;
            padding: 2px 10px;
            box-sizing: border-box;
            font-size: 13px;
            border-radius: 900px 900px 900px 900px;
            &.type1 {
              color: #007aff;
              background: #e6f2ff;
            }
            &.type2 {
              color: #17be6b;
              background: #ebfff1;
            }
            &.type3 {
              color: #7262fd;
              background: rgba(114, 98, 253, 0.12);
            }
            &.type4 {
              color: #262626;
              background: #f5f5f5;
            }
          }
        }
      }
    }
  }
  .images2 {
    margin-top: 16px;
    .images2-item {
      display: flex;
      flex-wrap: wrap;
      margin-bottom: 18px;
      .images2-item-li {
        margin-right: 16px;
        margin-bottom: 16px;
      }
    }
    img {
      margin-right: 16px;
      width: 160px;
      height: 90px;
      cursor: pointer;
    }
  }
}
</style>

<style lang="scss">
.images2-item-li {
  .ant-image-img {
    height: 90px;
  }
}
.result-content .class-structure {
  font-size: 14px;
}
</style>
