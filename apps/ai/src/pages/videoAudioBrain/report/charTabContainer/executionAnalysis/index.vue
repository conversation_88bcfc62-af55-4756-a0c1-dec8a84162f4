<template>
  <div class="execution-container">
    <ysEmpt v-if="!executionData.length" />
    <div v-else class="execution" id="execution"></div>
  </div>
</template>

<script setup lang="ts">
import { watch, inject, Ref, computed, nextTick } from "vue";
import * as echarts from "echarts";
import { ysEmpt } from "@ys/ui";

const roleChats = inject<Ref<any>>("roleChats");
const executionData = computed(() => {
  const list = roleChats?.value[3];
  const find30 = list.find((item: any) => item.questionType == 30);
  if (find30) {
    const arr = JSON.parse(find30.answer);

    const result = arr.reduce((acc: any, item: any) => {
      const degree = item["执行程度"];
      if (degree) {
        acc[degree]++;
      }
      return acc;
    }, {
      "优": 0,
      "良": 0,
      "差": 0,
      "未": 0
    });
    return result;
  }
  return [];
});

const initEcharts = () => {
  const colorMap = {
    "优": "#007AFF",
    "良": "#17BE6B",
    "差": "#FFAA00",
    "未": "#65789B"
  }

  console.log('executionData.value: ', executionData.value);

  const seriesData = Object.entries(executionData.value).map(([title, num]) => ({
    value: num,
    name: `${title} ${num}`,
    itemStyle: {
      color: colorMap[title]
    }
  }));
  console.log('seriesData: ', seriesData);

  const chartDom = document.getElementById("execution")!;
  const myChart = echarts.init(chartDom);
  let option: any;

  option = {
    legend: {
      bottom: '0%',
      left: 'center',
      icon: 'circle',
      itemWidth: 10,
      itemHeight: 10,
      itemGap: 20,
      textStyle: {
        color: '#333',
        fontSize: 18
      }
    },
    series: [
      {
        name: '执行程度',
        type: 'pie',
        radius: ['50%', '70%'], // 环形图内外半径
        center: ['50%', '40%'], // 将图表中心点向上移动
        avoidLabelOverlap: true, // 避免标签重叠
        itemStyle: {
          borderRadius: 0,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: true,
          position: 'outside', // 将标签放在外部
          formatter: '{b}', // 只显示名称和值
          fontSize: 14,
          color: "#262626",
          padding: 3, // 增加一些内边距
          backgroundColor: 'transparent', // 透明背景
          distance: 5, // 增加标签到饼图的距离
        },
        labelLine: {
          show: true, // 显示引导线
          length: 10, // 一段长度
          length2: 5, // 二段长度
          smooth: true // 平滑引导线
        },
        data: seriesData.map(item => {
          if (item.value === 0) {
            return {
              ...item,
              label: { show: false },
              labelLine: { show: false }
            };
          }
          return item;
        })
      }
    ]
  };

  option && myChart.setOption(option);
};

watch(
  () => roleChats?.value,
  () => {
    nextTick(() => {
      initEcharts();
    });
  },
  { immediate: true, deep: true }
);
</script>

<style lang="scss" scoped>
.execution-container {
  position: relative;
  width: 100%;
  height: 100%;
}
.execution {
  width: 100%;
  height: 400px;
  // margin-top: 50px;
}
</style>